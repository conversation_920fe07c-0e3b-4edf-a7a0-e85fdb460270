-- 日历功能相关表结构
-- 支持2021-2027年的日历查询
-- 后台提供类似gToHCalendar的接口，提前拉取并存储基础日历数据

-- 日历基础数据表 - 存储公历和伊斯兰历的对应关系（按月存储）
CREATE TABLE `calendar_dates` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `gregorian_year` int(11) NOT NULL COMMENT '公历年份',
  `gregorian_month` tinyint(3) NOT NULL COMMENT '公历月份(1-12)',
  `hijri_year` int(11) NOT NULL COMMENT '对应的伊斯兰历年份',
  `hijri_month` tinyint(3) NOT NULL COMMENT '对应的伊斯兰历月份(1-12)',
  `hijri_month_name_en` varchar(50) NOT NULL DEFAULT '' COMMENT '伊斯兰历月份英文名',
  `hijri_month_name_ar` varchar(50) NOT NULL DEFAULT '' COMMENT '伊斯兰历月份阿拉伯文名',
  `calendar_method` varchar(20) NOT NULL DEFAULT 'HJCoSA' COMMENT '日历计算方法(HJCoSA/UAQ/DIYANET/MATHEMATICAL)',
  `month_data` longtext NOT NULL COMMENT '该月的完整日历数据(JSON格式，存储API返回的原始数据)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_gregorian_month_method` (`gregorian_year`, `gregorian_month`, `calendar_method`),
  KEY `idx_gregorian_year_month` (`gregorian_year`, `gregorian_month`),
  KEY `idx_hijri_year_month` (`hijri_year`, `hijri_month`),
  KEY `idx_calendar_method` (`calendar_method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历基础数据表-按月存储公历伊斯兰历对应关系';

-- 节假日表 - 存储Hari Besar & Libur Nasional
CREATE TABLE `calendar_holidays` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `language_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '节假日名称',
  `description` text COMMENT '节假日描述',
  `holiday_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '节假日类型: 1-伊斯兰节日, 2-国家法定节假日, 3-其他节日',
  `gregorian_date` date NOT NULL COMMENT '公历日期',
  `hijri_date` varchar(10) NOT NULL DEFAULT '' COMMENT '伊斯兰历日期(DD-MM-YYYY格式)',
  `hijri_year` int(11) NOT NULL COMMENT '伊斯兰历年份',
  `hijri_month` tinyint(3) NOT NULL COMMENT '伊斯兰历月份(1-12)',
  `hijri_day` tinyint(3) NOT NULL COMMENT '伊斯兰历日期(1-30)',
  `is_recurring` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否每年重复: 0-否, 1-是',
  `is_clickable` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否可点击跳转: 0-否, 1-是',
  `jump_url` varchar(500) NOT NULL DEFAULT '' COMMENT '跳转链接URL',
  `jump_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '跳转类型: 1-内部页面, 2-外部链接, 3-弹窗详情',
  `content_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联内容ID(如文章ID)',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
  `data_source` tinyint(3) NOT NULL DEFAULT '1' COMMENT '数据来源: 1-API获取, 2-人工录入, 3-爬虫获取',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建/编辑管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_language_date` (`language_id`, `gregorian_date`),
  KEY `idx_hijri_date` (`hijri_year`, `hijri_month`, `hijri_day`),
  KEY `idx_type_status` (`holiday_type`, `status`),
  KEY `idx_gregorian_date` (`gregorian_date`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节假日表-Hari Besar & Libur Nasional';

-- 斋戒日表 - 存储Puasa信息
CREATE TABLE `calendar_fasting` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `language_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '斋戒名称',
  `description` text COMMENT '斋戒描述',
  `fasting_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '斋戒类型: 1-斋月, 2-圣行斋戒, 3-特殊斋戒',
  `gregorian_date` date NOT NULL COMMENT '公历日期',
  `hijri_date` varchar(10) NOT NULL DEFAULT '' COMMENT '伊斯兰历日期(DD-MM-YYYY格式)',
  `hijri_year` int(11) NOT NULL COMMENT '伊斯兰历年份',
  `hijri_month` tinyint(3) NOT NULL COMMENT '伊斯兰历月份(1-12)',
  `hijri_day` tinyint(3) NOT NULL COMMENT '伊斯兰历日期(1-30)',
  `is_recurring` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否每年重复: 0-否, 1-是',
  `is_clickable` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否可点击跳转: 0-否, 1-是',
  `jump_url` varchar(500) NOT NULL DEFAULT '' COMMENT '跳转链接URL',
  `jump_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '跳转类型: 1-内部页面, 2-外部链接, 3-弹窗详情',
  `content_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联内容ID(如文章ID)',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
  `data_source` tinyint(3) NOT NULL DEFAULT '1' COMMENT '数据来源: 1-API获取, 2-人工录入, 3-爬虫获取',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建/编辑管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_language_date` (`language_id`, `gregorian_date`),
  KEY `idx_hijri_date` (`hijri_year`, `hijri_month`, `hijri_day`),
  KEY `idx_type_status` (`fasting_type`, `status`),
  KEY `idx_gregorian_date` (`gregorian_date`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='斋戒日表-Puasa信息';

-- 特殊事件详情表 - 存储可点击跳转的事件详细信息
CREATE TABLE `calendar_event_details` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `language_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
  `event_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '事件类型: 1-节假日, 2-斋戒日',
  `event_id` int(11) unsigned NOT NULL COMMENT '关联事件ID(holiday_id或fasting_id)',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '事件标题',
  `content` longtext COMMENT '事件详细内容(支持HTML)',
  `summary` text COMMENT '事件摘要',
  `image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '事件图片URL',
  `video_url` varchar(500) NOT NULL DEFAULT '' COMMENT '事件视频URL',
  `audio_url` varchar(500) NOT NULL DEFAULT '' COMMENT '事件音频URL',
  `tags` varchar(500) NOT NULL DEFAULT '' COMMENT '标签，逗号分隔',
  `author` varchar(100) NOT NULL DEFAULT '' COMMENT '作者',
  `source` varchar(255) NOT NULL DEFAULT '' COMMENT '来源',
  `view_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '查看次数',
  `like_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `share_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分享次数',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建/编辑管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_type_id` (`event_type`, `event_id`),
  KEY `idx_language_status` (`language_id`, `status`),
  KEY `idx_status_created` (`status`, `created_at`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_like_count` (`like_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特殊事件详情表';


